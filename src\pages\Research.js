import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FaRobot, FaBrain, FaEye, FaLanguage, FaChartLine, FaExternalLinkAlt, FaArrowLeft } from 'react-icons/fa';
import '../styles/Research.css';
import '../styles/PageLayout.css';

const Research = () => {
  const [activeProject, setActiveProject] = useState(0);

  const researchAreas = [
    {
      icon: <FaRobot />,
      title: "Breast Cancer Gene Prediction",
      description: "Using DELSTM and differential evolution models to predict influential genes in breast cancer development."
    },
    {
      icon: <FaBrain />,
      title: "Machine Learning",
      description: "Developing advanced ML algorithms for healthcare applications and data clustering techniques."
    },
    {
      icon: <FaEye />,
      title: "Network Security",
      description: "Enhancing cyber attack detection using adaptive regression techniques in network traffic analysis."
    },
    {
      icon: <FaLanguage />,
      title: "Data Science",
      description: "Applying data science methodologies to solve complex problems in healthcare and cybersecurity."
    }
  ];

  const projects = [
    {
      title: "DELSTM Model for Breast Cancer Gene Prediction",
      description: "Developing a novel DELSTM model to predict the most influenced genes in breast cancer development for early detection and treatment.",
      status: "Published",
      funding: "University Research Grant",
      collaborators: ["Kanadam Karteeka Pavan", "Acharya Nagarjuna University"],
      publications: 2,
      image: "🧬"
    },
    {
      title: "Cyber Attack Detection in Network Traffic",
      description: "Enhancing network security through adaptive regression techniques for improved cyber attack detection capabilities.",
      status: "Published",
      funding: "Collaborative Research",
      collaborators: ["Dr. Talluri Sunil Kumar", "Multiple Institutions"],
      publications: 1,
      image: "🔒"
    },
    {
      title: "AI-Driven Personal Assistant",
      description: "Developing an AI-driven personal assistant with multi-platform integration capabilities for enhanced user experience.",
      status: "Patent Filed",
      funding: "Innovation Grant",
      collaborators: ["Research Team"],
      publications: 1,
      image: "🤖"
    }
  ];

  return (
    <div className="page-container">
      {/* Page Header */}
      <motion.div
        className="page-header"
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container">
          <Link to="/" className="back-link">
            <FaArrowLeft />
            Back to Home
          </Link>
          <h1 className="page-title">Research & Innovation</h1>
          <p className="page-subtitle">Explore my research areas, current projects, and innovations</p>
        </div>
      </motion.div>

      <section className="section research">
        <div className="container">
          {/* Research Areas */}
          <div className="research-areas">
            <motion.h3
              className="subsection-title"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Research Areas
            </motion.h3>
            
            <div className="areas-grid">
              {researchAreas.map((area, index) => (
                <motion.div
                  key={area.title}
                  className="area-card"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <div className="area-icon">{area.icon}</div>
                  <h4>{area.title}</h4>
                  <p>{area.description}</p>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Current Projects */}
          <div className="current-projects">
            <motion.h3
              className="subsection-title"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              Current Projects
            </motion.h3>

            <motion.div
              className="projects-container"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
            >
              <div className="project-tabs">
                {projects.map((project, index) => (
                  <button
                    key={project.title}
                    className={`project-tab ${activeProject === index ? 'active' : ''}`}
                    onClick={() => setActiveProject(index)}
                  >
                    <span className="tab-icon">{project.image}</span>
                    <span className="tab-title">{project.title}</span>
                  </button>
                ))}
              </div>

              <motion.div
                className="project-content"
                key={activeProject}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="project-header">
                  <div className="project-icon">{projects[activeProject].image}</div>
                  <div className="project-info">
                    <h4>{projects[activeProject].title}</h4>
                    <span className={`project-status ${projects[activeProject].status.toLowerCase()}`}>
                      {projects[activeProject].status}
                    </span>
                  </div>
                </div>

                <p className="project-description">
                  {projects[activeProject].description}
                </p>

                <div className="project-details">
                  <div className="detail-item">
                    <strong>Funding:</strong> {projects[activeProject].funding}
                  </div>
                  <div className="detail-item">
                    <strong>Collaborators:</strong> {projects[activeProject].collaborators.join(', ')}
                  </div>
                  <div className="detail-item">
                    <strong>Publications:</strong> {projects[activeProject].publications} papers
                  </div>
                </div>

                <button className="btn btn-outline project-link">
                  <FaExternalLinkAlt />
                  View Project Details
                </button>
              </motion.div>
            </motion.div>
          </div>

          {/* Research Impact */}
          <motion.div
            className="research-impact"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            <div className="impact-card">
              <FaChartLine className="impact-icon" />
              <div className="impact-content">
                <h4>Research Impact</h4>
                <p>
                  My research focuses on practical applications of AI in healthcare and cybersecurity,
                  contributing to early cancer detection methods and enhanced network security protocols.
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Research;
