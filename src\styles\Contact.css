.contact {
  background: var(--background-light);
  padding: 6rem 0;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

/* Contact Information */
.contact-info h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.contact-description {
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: 2.5rem;
  font-size: 1.1rem;
}

.contact-details {
  margin-bottom: 3rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: var(--transition-medium);
}

.contact-item:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.contact-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.contact-text h4 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.contact-text p {
  color: var(--text-secondary);
  margin: 0;
  white-space: pre-line;
}

.contact-link {
  color: var(--secondary-color);
  text-decoration: none;
  transition: var(--transition-fast);
}

.contact-link:hover {
  text-decoration: underline;
}

/* Office Hours */
.office-hours {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  margin-bottom: 2rem;
}

.office-hours h4 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

.hours-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.hour-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.hour-item:last-child {
  border-bottom: none;
}

.day {
  font-weight: 600;
  color: var(--primary-color);
}

.time {
  color: var(--text-secondary);
}

/* Social Links */
.social-links {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
}

.social-links h4 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

.social-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: 10px;
  color: var(--text-primary);
  text-decoration: none;
  transition: var(--transition-medium);
  font-weight: 500;
}

.social-link:hover {
  border-color: var(--social-color);
  color: var(--social-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.social-link span {
  font-size: 0.9rem;
}

/* Contact Form */
.contact-form-container {
  background: white;
  padding: 3rem;
  border-radius: 20px;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
}

.contact-form h3 {
  color: var(--primary-color);
  margin-bottom: 2rem;
  font-size: 1.8rem;
}

.form-group {
  margin-bottom: 2rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: 10px;
  font-size: 1rem;
  font-family: inherit;
  transition: var(--transition-fast);
  resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--text-secondary);
}

.submit-btn {
  width: 100%;
  padding: 1.2rem 2rem;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.success-message {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
  border-radius: 10px;
  text-align: center;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contact-content {
    gap: 3rem;
  }
  
  .contact-form-container {
    padding: 2.5rem;
  }
}

@media (max-width: 768px) {
  .contact {
    padding: 4rem 0;
  }
  
  .contact-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .contact-item {
    padding: 1.25rem;
  }
  
  .social-grid {
    grid-template-columns: 1fr;
  }
  
  .contact-form-container {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .contact-item {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .office-hours,
  .social-links {
    padding: 1.5rem;
  }
  
  .hour-item {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .contact-form-container {
    padding: 1.5rem;
  }
  
  .form-group {
    margin-bottom: 1.5rem;
  }
}
