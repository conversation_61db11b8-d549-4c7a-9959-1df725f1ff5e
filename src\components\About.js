import React from 'react';
import { motion } from 'framer-motion';
import { FaGraduationCap, FaAward, Fa<PERSON><PERSON><PERSON>, FaBookOpen } from 'react-icons/fa';
import '../styles/About.css';

const About = () => {
  const stats = [
    {
      icon: <FaGraduationCap />,
      number: "15+",
      label: "Years Teaching"
    },
    {
      icon: <FaBookOpen />,
      number: "50+",
      label: "Publications"
    },
    {
      icon: <FaAward />,
      number: "12",
      label: "Awards"
    },
    {
      icon: <FaUsers />,
      number: "200+",
      label: "Students Mentored"
    }
  ];

  const expertise = [
    "Artificial Intelligence",
    "Machine Learning",
    "Deep Learning",
    "Computer Vision",
    "Natural Language Processing",
    "Data Science",
    "Algorithm Design",
    "Software Engineering"
  ];

  return (
    <section id="about" className="section about">
      <div className="container">
        <motion.h2
          className="section-title"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          About Me
        </motion.h2>

        <div className="about-content">
          <motion.div
            className="about-text"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="about-intro">
              <h3>Passionate Educator & Researcher</h3>
              <p>
                With over 15 years of experience in academia, I am dedicated to advancing the frontiers 
                of computer science through innovative research and inspiring education. My work focuses 
                on the intersection of artificial intelligence and real-world applications.
              </p>
              <p>
                I believe in fostering a collaborative learning environment where students are encouraged 
                to think critically, explore new ideas, and develop the skills necessary to become the 
                next generation of technology leaders.
              </p>
            </div>

            <div className="expertise-section">
              <h4>Areas of Expertise</h4>
              <div className="expertise-grid">
                {expertise.map((skill, index) => (
                  <motion.div
                    key={skill}
                    className="expertise-item"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    {skill}
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          <motion.div
            className="about-image"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="image-placeholder">
              <div className="placeholder-content">
                <div className="placeholder-icon">🎓</div>
                <p>Academic Portrait</p>
              </div>
            </div>
          </motion.div>
        </div>

        <motion.div
          className="stats-section"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <div className="stats-grid">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                className="stat-card"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="stat-icon">{stat.icon}</div>
                <div className="stat-number">{stat.number}</div>
                <div className="stat-label">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
